import Foundation

/// Manages application configuration persistence
@Observable
class ConfigurationManager {
    var configuration: AppConfiguration
    
    private let configurationURL: URL
    private let fileManager = FileManager.default
    
    init() {
        // Get application support directory
        let appSupportURL = fileManager.urls(for: .applicationSupportDirectory, 
                                           in: .userDomainMask).first!
        let nexusDirectory = appSupportURL.appendingPathComponent("Nexus")
        
        // Create directory if it doesn't exist
        try? fileManager.createDirectory(at: nexusDirectory, 
                                       withIntermediateDirectories: true)
        
        configurationURL = nexusDirectory.appendingPathComponent("configuration.json")
        
        // Load existing configuration or use default
        if let data = try? Data(contentsOf: configurationURL),
           let config = try? JSONDecoder().decode(AppConfiguration.self, from: data) {
            self.configuration = config
        } else {
            self.configuration = AppConfiguration.default
            saveConfiguration()
        }
    }
    
    /// Save current configuration to disk
    func saveConfiguration() {
        do {
            let data = try JSONEncoder().encode(configuration)
            try data.write(to: configurationURL)
        } catch {
            print("Failed to save configuration: \(error)")
        }
    }
    
    /// Update editor settings
    func updateEditorSettings(_ settings: EditorSettings) {
        configuration = AppConfiguration(
            hotkeys: configuration.hotkeys,
            editorSettings: settings,
            moduleSettings: configuration.moduleSettings
        )
        saveConfiguration()
    }
    
    /// Update module settings
    func updateModuleSettings(for moduleId: String, settings: ModuleSettings) {
        var moduleSettings = configuration.moduleSettings
        moduleSettings[moduleId] = settings
        
        configuration = AppConfiguration(
            hotkeys: configuration.hotkeys,
            editorSettings: configuration.editorSettings,
            moduleSettings: moduleSettings
        )
        saveConfiguration()
    }
    
    /// Get settings for a specific module
    func moduleSettings(for moduleId: String) -> ModuleSettings {
        return configuration.moduleSettings[moduleId] ?? ModuleSettings()
    }
    
    /// Reset configuration to defaults
    func resetToDefaults() {
        configuration = AppConfiguration.default
        saveConfiguration()
    }
}
