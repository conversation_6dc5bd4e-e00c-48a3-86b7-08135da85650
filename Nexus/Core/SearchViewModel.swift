import Foundation
import SwiftUI
import Combine

/// View model for the main search interface
@Observable
class SearchViewModel {
    var searchText = ""
    var searchResults: [SearchResult] = []
    var isSearching = false
    var selectedResultIndex = 0
    
    private let moduleManager: ModuleManager
    private var searchCancellable: AnyCancellable?
    private let debounceInterval: TimeInterval = 0.2

    init(moduleManager: ModuleManager) {
        self.moduleManager = moduleManager
        setupSearchDebouncing()
    }

    /// Setup debounced search to avoid excessive API calls
    private func setupSearchDebouncing() {
        // Note: In @Observable, we need to manually set up debouncing
        // This will be implemented when we integrate with the UI
    }

    /// Manually trigger search (to be called from UI)
    func triggerSearch() {
        Task {
            await performSearch(searchText)
        }
    }
    
    /// Perform the actual search
    @MainActor
    private func performSearch(_ query: String) async {
        guard !query.isEmpty else {
            searchResults = []
            isSearching = false
            return
        }
        
        isSearching = true
        selectedResultIndex = 0
        
        do {
            let results = await moduleManager.search(query)
            searchResults = results
        } catch {
            print("Search error: \(error)")
            searchResults = []
        }
        
        isSearching = false
    }
    
    /// Execute the action for the currently selected result
    func executeSelectedResult() async {
        guard selectedResultIndex < searchResults.count else { return }
        let result = searchResults[selectedResultIndex]
        await executeResult(result)
    }
    
    /// Execute a specific search result action
    func executeResult(_ result: SearchResult) async {
        switch result.action {
        case .openNote(let id):
            // Will be implemented when Notes module is ready
            print("Opening note: \(id)")
            
        case .calculate(let expression):
            // Copy result to clipboard
            let pasteboard = NSPasteboard.general
            pasteboard.clearContents()
            pasteboard.setString(expression, forType: .string)
            
        case .copyToClipboard(let text):
            let pasteboard = NSPasteboard.general
            pasteboard.clearContents()
            pasteboard.setString(text, forType: .string)
            
        case .launchApplication(let path):
            NSWorkspace.shared.openApplication(at: URL(fileURLWithPath: path), 
                                             configuration: NSWorkspace.OpenConfiguration()) { _, _ in }
            
        case .custom(let action):
            await action()
        }
        
        // Clear search after action
        await MainActor.run {
            searchText = ""
            searchResults = []
        }
    }
    
    /// Navigate selection up
    func selectPrevious() {
        if selectedResultIndex > 0 {
            selectedResultIndex -= 1
        }
    }
    
    /// Navigate selection down
    func selectNext() {
        if selectedResultIndex < searchResults.count - 1 {
            selectedResultIndex += 1
        }
    }
    
    /// Clear search
    func clearSearch() {
        searchText = ""
        searchResults = []
        selectedResultIndex = 0
    }
}
