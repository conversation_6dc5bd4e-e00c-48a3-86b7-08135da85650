import SwiftUI

// Environment keys for the new @Observable objects
struct SearchViewModelKey: EnvironmentKey {
    static let defaultValue = SearchViewModel(moduleManager: ModuleManager())
}

struct ModuleManagerKey: EnvironmentKey {
    static let defaultValue = ModuleManager()
}

struct ConfigurationManagerKey: EnvironmentKey {
    static let defaultValue = ConfigurationManager()
}

extension EnvironmentValues {
    var searchViewModel: SearchViewModel {
        get { self[SearchViewModelKey.self] }
        set { self[SearchViewModelKey.self] = newValue }
    }
    
    var moduleManager: ModuleManager {
        get { self[ModuleManagerKey.self] }
        set { self[ModuleManagerKey.self] = newValue }
    }
    
    var configurationManager: ConfigurationManager {
        get { self[ConfigurationManagerKey.self] }
        set { self[ConfigurationManagerKey.self] = newValue }
    }
}
