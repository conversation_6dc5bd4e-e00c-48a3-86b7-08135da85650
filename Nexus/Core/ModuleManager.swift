import Foundation
import SwiftUI
import Combine
import KeyboardShortcuts

/// Manages all Nexus modules and coordinates their operations
@Observable
class ModuleManager {
    private var modules: [String: NexusModule] = [:]
    private var cancellables = Set<AnyCancellable>()
    
    /// All registered modules
    var allModules: [NexusModule] {
        Array(modules.values)
    }
    
    /// Currently enabled modules
    var enabledModules: [NexusModule] {
        modules.values.filter { $0.isEnabled }
    }
    
    /// Register a new module
    func register<T: NexusModule>(_ module: T) {
        modules[module.identifier] = module
    }
    
    /// Unregister a module
    func unregister(identifier: String) {
        modules.removeValue(forKey: identifier)
    }
    
    /// Get a specific module by identifier
    func module(for identifier: String) -> NexusModule? {
        return modules[identifier]
    }
    
    /// Activate all enabled modules
    func activateModules() async {
        await withTaskGroup(of: Void.self) { group in
            for module in enabledModules {
                group.addTask {
                    do {
                        try await module.activate()
                    } catch {
                        print("Failed to activate module \(module.identifier): \(error)")
                    }
                }
            }
        }
    }
    
    /// Deactivate all modules
    func deactivateModules() async {
        await withTaskGroup(of: Void.self) { group in
            for module in allModules {
                group.addTask {
                    await module.deactivate()
                }
            }
        }
    }
    
    /// Search across all enabled modules
    func search(_ query: String) async -> [SearchResult] {
        guard !query.isEmpty else { return [] }
        
        return await withTaskGroup(of: [SearchResult].self) { group in
            for module in enabledModules {
                group.addTask {
                    await module.search(query)
                }
            }
            
            var allResults: [SearchResult] = []
            for await results in group {
                allResults.append(contentsOf: results)
            }
            
            // Sort results by relevance (simple title matching for now)
            return allResults.sorted { result1, result2 in
                let query = query.lowercased()
                let title1 = result1.title.lowercased()
                let title2 = result2.title.lowercased()
                
                // Exact matches first
                if title1 == query && title2 != query { return true }
                if title2 == query && title1 != query { return false }
                
                // Starts with query
                if title1.hasPrefix(query) && !title2.hasPrefix(query) { return true }
                if title2.hasPrefix(query) && !title1.hasPrefix(query) { return false }
                
                // Contains query
                if title1.contains(query) && !title2.contains(query) { return true }
                if title2.contains(query) && !title1.contains(query) { return false }
                
                // Alphabetical fallback
                return title1 < title2
            }
        }
    }
    
    /// Handle keyboard shortcuts
    func handleKeyboardShortcut(_ shortcut: KeyboardShortcuts.Name) -> Bool {
        for module in enabledModules {
            if module.handleKeyboardShortcut(shortcut) {
                return true
            }
        }
        return false
    }
}
