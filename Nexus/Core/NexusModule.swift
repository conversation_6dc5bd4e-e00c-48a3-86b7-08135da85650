import Foundation
import SwiftUI
import KeyboardShortcuts

/// Core protocol that all Nexus modules must implement
protocol NexusModule {
    /// Unique identifier for the module
    var identifier: String { get }
    
    /// Display name shown to users
    var displayName: String { get }
    
    /// Whether the module is currently enabled
    var isEnabled: Bool { get set }
    
    /// Icon name for the module (SF Symbol)
    var iconName: String { get }
    
    /// Activate the module (called when app starts)
    func activate() async throws
    
    /// Deactivate the module (called when app stops)
    func deactivate() async
    
    /// Search for items matching the query
    func search(_ query: String) async -> [SearchResult]
    
    /// Handle keyboard shortcuts specific to this module
    func handleKeyboardShortcut(_ shortcut: KeyboardShortcuts.Name) -> Bool
    
    /// Get module-specific settings view
    func settingsView() -> AnyView?
}

/// Default implementations for optional methods
extension NexusModule {
    func handleKeyboardShortcut(_ shortcut: KeyboardShortcuts.Name) -> Bool {
        return false
    }
    
    func settingsView() -> AnyView? {
        return nil
    }
}

/// Represents a search result from any module
struct SearchResult: Identifiable, Hashable {
    let id = UUID()
    let title: String
    let subtitle: String?
    let iconName: String
    let moduleIdentifier: String
    let action: SearchAction
    
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    static func == (lhs: SearchResult, rhs: SearchResult) -> Bool {
        lhs.id == rhs.id
    }
}

/// Actions that can be performed on search results
enum SearchAction {
    case openNote(id: UUID)
    case calculate(expression: String)
    case copyToClipboard(text: String)
    case launchApplication(path: String)
    case custom(action: () async -> Void)
}
