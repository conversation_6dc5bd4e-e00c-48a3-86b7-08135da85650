import SwiftUI

struct ContentView: View {
    @Environment(\.searchViewModel) var searchViewModel: SearchViewModel
    @Environment(\.moduleManager) var moduleManager: ModuleManager
    @Environment(\.configurationManager) var configurationManager: ConfigurationManager
    @State private var searchText = ""

    var body: some View {
        VStack(spacing: 0) {
            // Search bar
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)

                TextField("Search Nexus...", text: $searchText)
                    .textFieldStyle(.plain)
                    .font(.system(size: 16))
                    .onSubmit {
                        Task {
                            await searchViewModel.executeSelectedResult()
                        }
                    }
                    .onChange(of: searchText) { _, newValue in
                        searchViewModel.searchText = newValue
                        searchViewModel.triggerSearch()
                    }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color(NSColor.controlBackgroundColor))

            Divider()

            // Content area
            ScrollView {
                VStack(alignment: .leading, spacing: 8) {
                    if searchText.isEmpty {
                        // Welcome message
                        VStack(spacing: 16) {
                            Image(systemName: "command.circle")
                                .font(.system(size: 48))
                                .foregroundColor(.accentColor)

                            Text("Welcome to Nexus")
                                .font(.title2)
                                .fontWeight(.semibold)

                            Text("Start typing to search notes, calculate, or launch apps")
                                .font(.body)
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)

                            // Show enabled modules
                            if !moduleManager.enabledModules.isEmpty {
                                VStack(alignment: .leading, spacing: 4) {
                                    Text("Available Modules:")
                                        .font(.caption)
                                        .foregroundColor(.secondary)

                                    ForEach(moduleManager.enabledModules, id: \.identifier) { module in
                                        HStack {
                                            Image(systemName: module.iconName)
                                                .foregroundColor(.accentColor)
                                            Text(module.displayName)
                                                .font(.caption)
                                        }
                                    }
                                }
                                .padding(.top, 16)
                            }
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.top, 40)
                    } else if searchViewModel.isSearching {
                        // Loading state
                        HStack {
                            ProgressView()
                                .scaleEffect(0.8)
                            Text("Searching...")
                                .font(.body)
                                .foregroundColor(.secondary)
                        }
                        .padding()
                    } else if searchViewModel.searchResults.isEmpty {
                        // No results
                        VStack(spacing: 8) {
                            Image(systemName: "magnifyingglass")
                                .font(.system(size: 32))
                                .foregroundColor(.secondary)
                            Text("No results found")
                                .font(.body)
                                .foregroundColor(.secondary)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.top, 40)
                    } else {
                        // Search results
                        LazyVStack(alignment: .leading, spacing: 4) {
                            ForEach(Array(searchViewModel.searchResults.enumerated()), id: \.element.id) { index, result in
                                SearchResultRow(
                                    result: result,
                                    isSelected: index == searchViewModel.selectedResultIndex
                                )
                                .onTapGesture {
                                    searchViewModel.selectedResultIndex = index
                                    Task {
                                        await searchViewModel.executeSelectedResult()
                                    }
                                }
                            }
                        }
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                    }
                }
                .padding()
            }
        }
        .frame(width: 400, height: 300)
        .onAppear {
            // Focus search field when view appears
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                NSApp.keyWindow?.makeFirstResponder(nil)
            }
        }
    }
}

struct SearchResultRow: View {
    let result: SearchResult
    let isSelected: Bool

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: result.iconName)
                .foregroundColor(.accentColor)
                .frame(width: 16, height: 16)

            VStack(alignment: .leading, spacing: 2) {
                Text(result.title)
                    .font(.body)
                    .lineLimit(1)

                if let subtitle = result.subtitle {
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                }
            }

            Spacer()
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(isSelected ? Color.accentColor.opacity(0.2) : Color.clear)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 6)
                .stroke(isSelected ? Color.accentColor : Color.clear, lineWidth: 1)
        )
    }
}

#Preview {
    let moduleManager = ModuleManager()
    let searchViewModel = SearchViewModel(moduleManager: moduleManager)
    let configurationManager = ConfigurationManager()

    return ContentView()
        .environment(\.searchViewModel, searchViewModel)
        .environment(\.moduleManager, moduleManager)
        .environment(\.configurationManager, configurationManager)
}
