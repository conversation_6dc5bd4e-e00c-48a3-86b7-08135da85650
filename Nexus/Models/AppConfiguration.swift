import Foundation
import KeyboardShortcuts

/// Core data models for Nexus

/// Represents a note in the system
struct Note: Identifiable, Codable {
    let id: UUID
    var content: String
    var title: String
    let createdAt: Date
    var modifiedAt: Date
    var isFavorite: Bool
    var isArchived: Bool

    init(
        id: UUID = UUID(),
        content: String = "",
        title: String = "",
        createdAt: Date = Date(),
        modifiedAt: Date = Date(),
        isFavorite: Bool = false,
        isArchived: Bool = false
    ) {
        self.id = id
        self.content = content
        self.title = title.isEmpty ? Note.generateTitle(from: content) : title
        self.createdAt = createdAt
        self.modifiedAt = modifiedAt
        self.isFavorite = isFavorite
        self.isArchived = isArchived
    }

    /// Generate a title from content
    static func generateTitle(from content: String) -> String {
        let lines = content.components(separatedBy: .newlines)
        let firstLine = lines.first?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""

        if firstLine.isEmpty {
            return "Untitled Note"
        }

        // Remove markdown heading syntax
        let title = firstLine.replacingOccurrences(of: "^#+\\s*", with: "", options: .regularExpression)

        // Limit length
        if title.count > 50 {
            return String(title.prefix(47)) + "..."
        }

        return title
    }

    /// Update the note's content and refresh title and modified date
    mutating func updateContent(_ newContent: String) {
        content = newContent
        title = Note.generateTitle(from: newContent)
        modifiedAt = Date()
    }
}

/// Represents a folder for organizing notes
struct Folder: Identifiable, Codable {
    let id: UUID
    var name: String
    var color: String?
    let createdAt: Date
    var parentId: UUID?

    init(
        id: UUID = UUID(),
        name: String,
        color: String? = nil,
        createdAt: Date = Date(),
        parentId: UUID? = nil
    ) {
        self.id = id
        self.name = name
        self.color = color
        self.createdAt = createdAt
        self.parentId = parentId
    }
}

/// Main application configuration
struct AppConfiguration: Codable {
    let hotkeys: [String: KeyboardShortcutData]
    let editorSettings: EditorSettings
    let moduleSettings: [String: ModuleSettings]
    
    static let `default` = AppConfiguration(
        hotkeys: [
            "toggleNexus": KeyboardShortcutData(key: "space", modifiers: ["command", "shift"])
        ],
        editorSettings: EditorSettings.default,
        moduleSettings: [:]
    )
}

/// Keyboard shortcut data for persistence
struct KeyboardShortcutData: Codable {
    let key: String
    let modifiers: [String]
}

/// Editor-specific settings
struct EditorSettings: Codable {
    let fontSize: Double
    let fontFamily: String
    let lineSpacing: Double
    let showLineNumbers: Bool
    let wrapText: Bool
    let markdownRenderingEnabled: Bool
    let syntaxHighlightingEnabled: Bool
    
    static let `default` = EditorSettings(
        fontSize: 14.0,
        fontFamily: "SF Mono",
        lineSpacing: 1.2,
        showLineNumbers: false,
        wrapText: true,
        markdownRenderingEnabled: false,
        syntaxHighlightingEnabled: true
    )
}

/// Generic module settings container
struct ModuleSettings: Codable {
    let settings: [String: AnyCodable]
    
    init(_ settings: [String: Any] = [:]) {
        self.settings = settings.mapValues { AnyCodable($0) }
    }
    
    subscript<T: Codable>(key: String, type: T.Type) -> T? {
        get {
            return settings[key]?.value as? T
        }
    }
    
    func value<T: Codable>(for key: String, type: T.Type, default defaultValue: T) -> T {
        return self[key, type] ?? defaultValue
    }
}

/// Type-erased codable wrapper for heterogeneous settings
struct AnyCodable: Codable {
    let value: Any
    
    init(_ value: Any) {
        self.value = value
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()
        
        if let bool = try? container.decode(Bool.self) {
            value = bool
        } else if let int = try? container.decode(Int.self) {
            value = int
        } else if let double = try? container.decode(Double.self) {
            value = double
        } else if let string = try? container.decode(String.self) {
            value = string
        } else if let array = try? container.decode([AnyCodable].self) {
            value = array.map { $0.value }
        } else if let dictionary = try? container.decode([String: AnyCodable].self) {
            value = dictionary.mapValues { $0.value }
        } else {
            throw DecodingError.dataCorruptedError(in: container, debugDescription: "Unsupported type")
        }
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()
        
        switch value {
        case let bool as Bool:
            try container.encode(bool)
        case let int as Int:
            try container.encode(int)
        case let double as Double:
            try container.encode(double)
        case let string as String:
            try container.encode(string)
        case let array as [Any]:
            try container.encode(array.map { AnyCodable($0) })
        case let dictionary as [String: Any]:
            try container.encode(dictionary.mapValues { AnyCodable($0) })
        default:
            throw EncodingError.invalidValue(value, EncodingError.Context(codingPath: [], debugDescription: "Unsupported type"))
        }
    }
}
