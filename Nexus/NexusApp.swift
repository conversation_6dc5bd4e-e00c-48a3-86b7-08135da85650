import SwiftUI
import KeyboardShortcuts

@main
struct NexusApp: App {
    @NSApplicationDelegateAdaptor(AppDelegate.self) var appDelegate

    var body: some Scene {
        Settings {
            EmptyView()
        }
    }
}

class AppDelegate: NSObject, NSApplicationDelegate {
    var statusItem: NSStatusItem?
    var popover: NSPopover?
    var moduleManager: ModuleManager!
    var searchViewModel: SearchViewModel!
    var configurationManager: ConfigurationManager!

    func applicationDidFinishLaunching(_ notification: Notification) {
        // Hide the app from the dock
        NSApp.setActivationPolicy(.accessory)

        // Initialize core components
        setupCoreComponents()

        // Setup menu bar
        setupMenuBar()

        // Setup global hotkeys
        setupGlobalHotkeys()

        // Activate modules
        Task {
            await moduleManager.activateModules()
        }
    }

    func applicationWillTerminate(_ notification: Notification) {
        Task {
            await moduleManager.deactivateModules()
        }
    }

    private func setupCoreComponents() {
        configurationManager = ConfigurationManager()
        moduleManager = ModuleManager()
        searchViewModel = SearchViewModel(moduleManager: moduleManager)
    }

    private func setupMenuBar() {
        // Create status item
        statusItem = NSStatusBar.system.statusItem(withLength: NSStatusItem.variableLength)

        if let button = statusItem?.button {
            button.image = NSImage(systemSymbolName: "command.circle", accessibilityDescription: "Nexus")
            button.action = #selector(togglePopover)
            button.target = self
        }

        // Create popover
        popover = NSPopover()
        popover?.contentSize = NSSize(width: 400, height: 300)
        popover?.behavior = .applicationDefined
        popover?.appearance = NSAppearance(named: .vibrantLight)

        let contentView = ContentView()
            .environment(\.searchViewModel, searchViewModel)
            .environment(\.moduleManager, moduleManager)
            .environment(\.configurationManager, configurationManager)

        popover?.contentViewController = NSHostingController(rootView: contentView)
    }

    private func setupGlobalHotkeys() {
        // Register the main toggle hotkey
        KeyboardShortcuts.onKeyUp(for: .toggleNexus) { [weak self] in
            self?.togglePopover()
        }
    }

    @objc func togglePopover() {
        guard let popover = popover else { return }

        if popover.isShown {
            popover.performClose(nil)
        } else {
            if let button = statusItem?.button {
                popover.show(relativeTo: button.bounds, of: button, preferredEdge: NSRectEdge.minY)

                // Focus the search field when popover opens
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    if let contentViewController = popover.contentViewController as? NSHostingController<ContentView> {
                        contentViewController.view.window?.makeFirstResponder(nil)
                    }
                }
            }
        }
    }
}

// Define the global hotkey
extension KeyboardShortcuts.Name {
    static let toggleNexus = Self("toggleNexus", default: .init(.space, modifiers: [.command, .shift]))
}
