import SwiftUI
import Markdown
import AppKit

/// Main editor view that combines text editing with optional Markdown rendering
struct EditorView: View {
    @Binding var content: String
    @State private var isMarkdownMode = false
    @State private var showingSettings = false
    
    let editorSettings: EditorSettings
    
    init(content: Binding<String>, editorSettings: EditorSettings = .default) {
        self._content = content
        self.editorSettings = editorSettings
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Editor toolbar
            HStack {
                // Mode toggle
                Picker("Mode", selection: $isMarkdownMode) {
                    Text("Plain Text").tag(false)
                    Text("Markdown").tag(true)
                }
                .pickerStyle(.segmented)
                .frame(width: 150)
                
                Spacer()
                
                // Settings button
                Button(action: { showingSettings.toggle() }) {
                    Image(systemName: "gear")
                }
                .buttonStyle(.borderless)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(Color(NSColor.controlBackgroundColor))
            
            Divider()
            
            // Editor content
            if isMarkdownMode && editorSettings.markdownRenderingEnabled {
                MarkdownRenderView(content: content)
            } else {
                NexusTextView(
                    text: $content,
                    font: NSFont(name: editorSettings.fontFamily, size: editorSettings.fontSize) ?? 
                          NSFont.monospacedSystemFont(ofSize: editorSettings.fontSize, weight: .regular)
                )
            }
        }
        .sheet(isPresented: $showingSettings) {
            EditorSettingsView(settings: editorSettings)
        }
    }
}

/// Markdown rendering view
struct MarkdownRenderView: View {
    let content: String
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 16) {
                if content.isEmpty {
                    Text("Start typing to see Markdown preview...")
                        .foregroundColor(.secondary)
                        .frame(maxWidth: .infinity, alignment: .center)
                        .padding(.top, 40)
                } else {
                    MarkdownContentView(content: content)
                }
            }
            .padding()
        }
        .background(Color(NSColor.textBackgroundColor))
    }
}

/// Renders Markdown content using swift-markdown
struct MarkdownContentView: View {
    let content: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            let document = Document(parsing: content)
            ForEach(Array(document.children.enumerated()), id: \.offset) { index, block in
                if let blockMarkup = block as? BlockMarkup {
                    MarkdownBlockView(block: blockMarkup)
                }
            }
        }
    }
}

/// Renders individual Markdown blocks
struct MarkdownBlockView: View {
    let block: BlockMarkup
    
    var body: some View {
        Group {
            switch block {
            case let heading as Heading:
                MarkdownHeadingView(heading: heading)
            case let paragraph as Paragraph:
                MarkdownParagraphView(paragraph: paragraph)
            case let codeBlock as CodeBlock:
                MarkdownCodeBlockView(codeBlock: codeBlock)
            case let list as ListItem:
                MarkdownListItemView(listItem: list)
            default:
                Text("Unsupported block type")
                    .foregroundColor(.secondary)
            }
        }
    }
}

/// Renders Markdown headings
struct MarkdownHeadingView: View {
    let heading: Heading
    
    var body: some View {
        let text = heading.children.compactMap { $0 as? Markdown.Text }.map { $0.string }.joined()
        
        Group {
            switch heading.level {
            case 1:
                SwiftUI.Text(text).font(.largeTitle).fontWeight(.bold)
            case 2:
                SwiftUI.Text(text).font(.title).fontWeight(.semibold)
            case 3:
                SwiftUI.Text(text).font(.title2).fontWeight(.medium)
            case 4:
                SwiftUI.Text(text).font(.title3).fontWeight(.medium)
            case 5:
                SwiftUI.Text(text).font(.headline)
            default:
                SwiftUI.Text(text).font(.subheadline).fontWeight(.medium)
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
}

/// Renders Markdown paragraphs
struct MarkdownParagraphView: View {
    let paragraph: Paragraph
    
    var body: some View {
        let text = paragraph.children.compactMap { $0 as? Markdown.Text }.map { $0.string }.joined()
        SwiftUI.Text(text)
            .font(.body)
            .frame(maxWidth: .infinity, alignment: .leading)
    }
}

/// Renders Markdown code blocks
struct MarkdownCodeBlockView: View {
    let codeBlock: CodeBlock
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            if let language = codeBlock.language, !language.isEmpty {
                SwiftUI.Text(language.uppercased())
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .leading)
            }

            SwiftUI.Text(codeBlock.code)
                .font(.system(.body, design: .monospaced))
                .padding(12)
                .background(Color(NSColor.controlBackgroundColor))
                .cornerRadius(6)
                .frame(maxWidth: .infinity, alignment: .leading)
        }
    }
}

/// Renders Markdown list items
struct MarkdownListItemView: View {
    let listItem: ListItem
    
    var body: some View {
        HStack(alignment: .top, spacing: 8) {
            SwiftUI.Text("•")
                .font(.body)
                .foregroundColor(.secondary)

            VStack(alignment: .leading, spacing: 4) {
                ForEach(Array(listItem.children.enumerated()), id: \.offset) { index, block in
                    if let blockMarkup = block as? BlockMarkup {
                        MarkdownBlockView(block: blockMarkup)
                    }
                }
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
}

/// Editor settings view
struct EditorSettingsView: View {
    let settings: EditorSettings
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        VStack(spacing: 20) {
            SwiftUI.Text("Editor Settings")
                .font(.title2)
                .fontWeight(.semibold)
            
            Form {
                Section("Appearance") {
                    HStack {
                        SwiftUI.Text("Font Size:")
                        Spacer()
                        SwiftUI.Text("\(Int(settings.fontSize))pt")
                            .foregroundColor(.secondary)
                    }

                    HStack {
                        SwiftUI.Text("Font Family:")
                        Spacer()
                        SwiftUI.Text(settings.fontFamily)
                            .foregroundColor(.secondary)
                    }
                }

                Section("Features") {
                    HStack {
                        SwiftUI.Text("Markdown Rendering:")
                        Spacer()
                        SwiftUI.Text(settings.markdownRenderingEnabled ? "Enabled" : "Disabled")
                            .foregroundColor(.secondary)
                    }

                    HStack {
                        SwiftUI.Text("Syntax Highlighting:")
                        Spacer()
                        SwiftUI.Text(settings.syntaxHighlightingEnabled ? "Enabled" : "Disabled")
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            Button("Close") {
                dismiss()
            }
            .buttonStyle(.borderedProminent)
        }
        .padding()
        .frame(width: 300, height: 400)
    }
}

#Preview {
    @State var sampleContent = """
    # Welcome to Nexus Editor
    
    This is a **plain-text first** editor with optional Markdown rendering.
    
    ## Features
    
    - Pure text editing by default
    - Optional Markdown preview
    - Syntax highlighting for code blocks
    - Keyboard-first navigation
    
    ```swift
    func example() {
        print("Hello, Nexus!")
    }
    ```
    
    The editor respects your text and never modifies it automatically.
    """
    
    return EditorView(content: $sampleContent)
        .frame(width: 600, height: 500)
}
