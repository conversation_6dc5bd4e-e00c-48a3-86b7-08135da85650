import SwiftUI
import AppKit

/// SwiftUI wrapper for NSTextView with plain-text configuration
struct NexusTextView: NSViewRepresentable {
    @Binding var text: String
    let isEditable: Bool
    let font: NSFont
    let textColor: NSColor
    let backgroundColor: NSColor
    
    init(
        text: Binding<String>,
        isEditable: Bool = true,
        font: NSFont = NSFont.monospacedSystemFont(ofSize: 14, weight: .regular),
        textColor: NSColor = .textColor,
        backgroundColor: NSColor = .textBackgroundColor
    ) {
        self._text = text
        self.isEditable = isEditable
        self.font = font
        self.textColor = textColor
        self.backgroundColor = backgroundColor
    }
    
    func makeNSView(context: Context) -> NSScrollView {
        let scrollView = NSScrollView()
        let textView = NSTextView()
        
        // Configure scroll view
        scrollView.hasVerticalScroller = true
        scrollView.hasHorizontalScroller = false
        scrollView.autohidesScrollers = true
        scrollView.borderType = .noBorder
        scrollView.documentView = textView
        
        // Configure text view for plain text
        configureTextView(textView)
        
        // Set up coordinator
        textView.delegate = context.coordinator
        
        return scrollView
    }
    
    func updateNSView(_ nsView: NSScrollView, context: Context) {
        guard let textView = nsView.documentView as? NSTextView else { return }
        
        // Update text if it differs from current content
        if textView.string != text {
            textView.string = text
        }
        
        // Update appearance
        textView.font = font
        textView.textColor = textColor
        textView.backgroundColor = backgroundColor
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    private func configureTextView(_ textView: NSTextView) {
        // Plain text configuration - disable all automatic features
        textView.isAutomaticQuoteSubstitutionEnabled = false
        textView.isAutomaticDashSubstitutionEnabled = false
        textView.isAutomaticTextReplacementEnabled = false
        textView.isAutomaticSpellingCorrectionEnabled = false
        textView.smartInsertDeleteEnabled = false
        textView.isRichText = false
        textView.usesRuler = false
        textView.allowsUndo = true
        textView.isVerticallyResizable = true
        textView.isHorizontallyResizable = false
        textView.textContainerInset = NSSize(width: 12, height: 12)
        
        // Configure text container
        textView.textContainer?.widthTracksTextView = true
        textView.textContainer?.containerSize = NSSize(width: 0, height: CGFloat.greatestFiniteMagnitude)
        
        // Set appearance
        textView.font = font
        textView.textColor = textColor
        textView.backgroundColor = backgroundColor
        textView.isEditable = isEditable
        textView.isSelectable = true
        
        // Set initial text
        textView.string = text
    }
    
    class Coordinator: NSObject, NSTextViewDelegate {
        let parent: NexusTextView
        
        init(_ parent: NexusTextView) {
            self.parent = parent
        }
        
        func textDidChange(_ notification: Notification) {
            guard let textView = notification.object as? NSTextView else { return }
            
            // Update binding when text changes
            DispatchQueue.main.async {
                self.parent.text = textView.string
            }
        }
        
        func textView(_ textView: NSTextView, doCommandBy commandSelector: Selector) -> Bool {
            // Handle special key commands if needed
            switch commandSelector {
            case #selector(NSResponder.insertNewline(_:)):
                // Allow normal newline behavior
                return false
            case #selector(NSResponder.insertTab(_:)):
                // Allow normal tab behavior
                return false
            default:
                return false
            }
        }
    }
}

/// Preview wrapper for NexusTextView
struct NexusTextView_Previews: PreviewProvider {
    @State static var sampleText = """
    # Sample Markdown Content
    
    This is a plain text editor that supports:
    - Pure text editing
    - No automatic formatting
    - Optional Markdown rendering
    
    ## Code Example
    
    ```swift
    func hello() {
        print("Hello, Nexus!")
    }
    ```
    
    The editor maintains the philosophy of plain text first.
    """
    
    static var previews: some View {
        VStack {
            Text("Nexus Text Editor")
                .font(.title2)
                .padding()
            
            NexusTextView(text: $sampleText)
                .frame(height: 300)
                .border(Color.gray, width: 1)
                .padding()
        }
        .frame(width: 500, height: 400)
    }
}
