# **Technical Product Requirements Document: Nexus for macOS**

*   **Version:** 1.2 (Developer-Ready - Revised)
*   **Status:** Draft
*   **Target:** To provide a detailed technical blueprint for the development of Nexus v1.0, focusing on architecture, implementation details, and specific feature mechanics with optimized technical solutions.

## 1. Vision & Guiding Technical Principles

Nexus is a native macOS utility that resurrects the spirit of hyper-efficient, lightweight, and user-controlled Windows applications like `minipad2`, reimagined for the modern Mac ecosystem.

*   **1.1. Native Performance is Non-Negotiable:** The application must be built exclusively with Swift, SwiftUI, and AppKit. No cross-platform frameworks will be used. Performance is measured in milliseconds and single-digit CPU percentages.
*   **1.2. Plain Text is the Default State:** The user is in control. The editor's default mode must be pure plain text, free from any unsolicited auto-formatting, "smart" features, or rich text conversions. Power features are opt-in, not forced.
*   **1.3. Keyboard-First, Not Just Keyboard-Friendly:** Every core action must be achievable faster with the keyboard than with a mouse. This principle will guide all UI/UX and interaction design.
*   **1.4. Modular & Decoupled Architecture:** The system must be designed as a series of independent modules managed by a lightweight core. This ensures stability, testability, and future extensibility through a protocol-based plugin system.
*   **1.5. Offline-First with Optional Cloud:** The application must be 100% functional offline. Cloud synchronization (iCloud) is a value-added feature, not a requirement for operation. External APIs must have robust fallback mechanisms.

## 2. Target Architecture & Technology Stack

| Category                  | Recommended Technology/Framework      | Justification & Implementation Notes                                                                                                                                                                             |
| ------------------------- | ------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Language**              | **Swift 5.9+**                        | Leverage modern concurrency (`async/await`), `@Observable` macro for state management, and strong type safety. Target macOS 14+ for modern SwiftUI features.                                                  |
| **UI Framework**          | **SwiftUI (Primary) + AppKit (Hybrid)** | SwiftUI for the main application layout, views, and settings. AppKit for high-performance, critical components: **1.** The core text editor (`NSTextView`). **2.** Menu bar integration (`NSStatusItem`). **3.** Global hotkey management. |
| **Data Persistence**      | **SQLite.swift + JSON Storage**       | Lighter alternative to Core Data for simple data models. SQLite.swift for structured queries, JSON files for configuration. Enables easier iCloud Document-based sync and better performance control.          |
| **Text Editing**          | **`NSTextView` via `NSViewRepresentable`** | The only viable path for a high-performance, fully customizable plain-text editor. SwiftUI's `TextEditor` lacks the necessary low-level controls.                                                                  |
| **State Management**      | **@Observable + Combine**             | Use modern `@Observable` macro for view models, Combine for reactive programming and async operations.                                                                                                            |
| **Dependency Management** | **Swift Package Manager (SPM)**       | Native, clean, and integrated with Xcode.                                                                                                                                                                        |
| **Recommended Packages**    | `apple/swift-markdown`, `Highlightr`, `KeyboardShortcuts`, `SQLite.swift` | **`swift-markdown`:** For parsing Markdown into an AST. **`Highlightr`:** For syntax highlighting. **`KeyboardShortcuts`:** For reliable global hotkeys. **`SQLite.swift`:** For lightweight database operations. |

## 3. Core Application Shell: Implementation Details

*   **3.1. Menu Bar Presence:**
    *   An `AppDelegate` will create and manage a single `NSStatusItem`.
    *   **Revised Approach:** Use `NSPopover` instead of manual window positioning for better system integration and multi-monitor support.
    *   The popover will be configured with `.behavior = .applicationDefined` and `.appearance = NSAppearance(named: .vibrantLight)` for proper visual integration.
    *   **Benefits:** Automatic positioning, proper focus management, and system-consistent behavior.
    
*   **3.2. Global Hotkey Activation:**
    *   **Revised Implementation:** Use the `KeyboardShortcuts` SPM package instead of deprecated Carbon APIs.
    *   ```swift
    import KeyboardShortcuts
    
    extension KeyboardShortcuts.Name {
        static let toggleNexus = Self("toggleNexus", default: .init(.space, modifiers: [.command, .shift]))
    }
    ```
    *   This provides modern, reliable global hotkey functionality with proper system integration and user customization support.
    *   Fallback to `CGEventTap` with accessibility permissions if needed for advanced scenarios.

*   **3.3. Unified Search:**
    *   A SwiftUI `TextField` at the top of the main window binds to an `@Observable` `SearchViewModel`.
    *   Using Combine's `.debounce()`, the view model will trigger a search request after a short typing pause (200ms for better responsiveness).
    *   The `SearchService` will iterate through all registered `Searchable` modules, using `TaskGroup` for concurrent searches with proper error handling and timeout mechanisms.

## 4. The Nexus Editor: A Plain-Text First Implementation

This is the most critical component. The design must reject the "helpful" but intrusive nature of modern rich-text editors.

*   **4.1. The Philosophy:** The editor is a container for pure characters. Formatting is a presentation layer, not a modification of the underlying data. The user must never be surprised by the application changing their text.
*   
*   **4.2. Default Mode: Pure Plain Text:**
    *   The `NSTextView` instance must be explicitly configured to disable all automatic substitutions and formatting.
    *   **Enhanced Implementation:**
        ```swift
        // Inside the NSViewRepresentable coordinator for the NSTextView
        textView.isAutomaticQuoteSubstitutionEnabled = false
        textView.isAutomaticDashSubstitutionEnabled = false
        textView.isAutomaticTextReplacementEnabled = false
        textView.isAutomaticSpellingCorrectionEnabled = false // User configurable
        textView.smartInsertDeleteEnabled = false
        textView.isRichText = false
        textView.usesRuler = false
        textView.allowsUndo = true
        textView.isVerticallyResizable = true
        textView.textContainerInset = NSSize(width: 12, height: 12)
        ```

*   **4.3. Opt-In Markdown Mode:**
    *   This is a *rendering mode*, not an editing mode. The underlying text remains pure Markdown.
    *   **Performance Optimization:** Use `NSTextStorage` subclass for efficient real-time rendering with debounced updates to prevent lag during typing.
    *   Implement custom `NSLayoutManager` for better control over code block rendering and syntax highlighting integration.

*   **4.4. Performance Monitoring:**
    *   Implement text editing performance benchmarks with metrics for:
        - Keystroke latency (target: <16ms)
        - Memory usage during large document editing
        - Syntax highlighting performance for code blocks

## 5. Data Architecture: Simplified and Efficient

### 5.1. Data Storage Strategy

**Primary Storage: SQLite + JSON Hybrid**
```swift
// Core entities stored in SQLite for efficient querying
struct Note {
    let id: UUID
    let content: String
    let createdAt: Date
    let modifiedAt: Date
    let folderIds: [UUID]
}

// Configuration and preferences in JSON
struct AppConfiguration: Codable {
    let hotkeys: [String: KeyboardShortcut]
    let editorSettings: EditorSettings
    let moduleSettings: [String: ModuleSettings]
}
```

**Benefits:**
- Lighter than Core Data for simple models
- Better performance control
- Easier debugging and data migration
- Simpler iCloud Document-based synchronization

### 5.2. iCloud Synchronization Strategy

**Document-Based Approach:**
- Store notes as individual `.nexus` files in iCloud Documents
- Use `NSDocument` architecture for automatic conflict resolution
- Maintain local SQLite index for fast searching and organization

## 6. Feature Modules: Revised Technical Implementation

### 6.1. Notes Hub

*   **Technical Implementation:**
    *   **Data Model:** SQLite tables for `notes`, `folders`, and `note_folders` (junction table).
    *   **UI Enhancement:** Implement custom `NSOutlineView` wrapped in `NSViewRepresentable` for better performance with large note collections.
    *   **Search Optimization:** Full-text search using SQLite FTS5 extension for instant results.

### 6.2. Smart Calculator

*   **Enhanced Implementation:**
    *   **Phase 1:** Natural language processing with `NSLinguisticTagger` for better pattern recognition.
    *   **Phase 2:** Safe expression evaluation using `NSExpression` with input sanitization.
    *   **Offline-First Currency:** Bundle basic exchange rates as fallback data, with optional API updates.
    *   **Unit Conversion:** Use `Measurement` and `Unit` classes for accurate conversions.
    *   ```swift
    let distance = Measurement(value: 100, unit: UnitLength.meters)
    let converted = distance.converted(to: .feet)
    ```

### 6.3. Clipboard Manager

*   **Revised Implementation:**
    *   **Efficient Monitoring:** Use `NSPasteboard` change count observation instead of polling:
    *   ```swift
    class ClipboardMonitor: ObservableObject {
        private var lastChangeCount = NSPasteboard.general.changeCount
        private var timer: Timer?
        
        func startMonitoring() {
            timer = Timer.scheduledTimer(withTimeInterval: 0.1) { _ in
                let currentCount = NSPasteboard.general.changeCount
                if currentCount != self.lastChangeCount {
                    self.handlePasteboardChange()
                    self.lastChangeCount = currentCount
                }
            }
        }
    }
    ```
    *   **Security Enhancement:** Implement optional encryption for sensitive clipboard data using `CryptoKit`.
    *   **Memory Management:** Implement LRU cache with configurable limits to prevent memory bloat.

### 6.4. Quick Launcher

*   **Enhanced Implementation:**
    *   **Smart Indexing:** Index system applications automatically using `NSWorkspace.shared.urlsForApplications`.
    *   **Fuzzy Search:** Implement fuzzy string matching for better search experience.
    *   **Recent Items:** Track and prioritize frequently launched items.

## 7. Protocol-Based Module System

### 7.1. Module Architecture

```swift
@Observable
protocol NexusModule {
    var identifier: String { get }
    var displayName: String { get }
    var isEnabled: Bool { get set }
    
    func activate() async throws
    func deactivate() async
    func search(_ query: String) async -> [SearchResult]
    func handleKeyboardShortcut(_ shortcut: KeyboardShortcuts.Name) -> Bool
}

class ModuleManager: ObservableObject {
    private var modules: [String: NexusModule] = [:]
    
    func register<T: NexusModule>(_ module: T) {
        modules[module.identifier] = module
    }
    
    func search(_ query: String) async -> [SearchResult] {
        await withTaskGroup(of: [SearchResult].self) { group in
            for module in modules.values where module.isEnabled {
                group.addTask {
                    await module.search(query)
                }
            }
            
            var allResults: [SearchResult] = []
            for await results in group {
                allResults.append(contentsOf: results)
            }
            return allResults
        }
    }
}
```

## 8. Security and Privacy Considerations

### 8.1. Data Protection
- Implement optional encryption for sensitive data using `CryptoKit`
- Secure clipboard history with automatic expiration for sensitive content
- Request minimal permissions and explain their necessity to users

### 8.2. Privacy Features
- Option to exclude specific applications from clipboard monitoring
- Automatic detection and special handling of password managers
- Local-only mode that disables all network features

## 9. Accessibility and User Experience

### 9.1. Accessibility Support
- Full VoiceOver support for all UI elements
- High contrast mode support
- Keyboard navigation for all features
- Customizable font sizes and contrast

### 9.2. Performance Benchmarks
- Application launch time: <500ms
- Search response time: <100ms for 10k+ items
- Memory usage: <50MB baseline, <200MB with large clipboard history
- CPU usage: <5% idle, <15% during intensive operations

## 10. Testing Strategy

### 10.1. Unit Testing
- Protocol-based architecture enables comprehensive mocking
- Test coverage target: >80% for core modules
- Performance regression tests for critical paths

### 10.2. Integration Testing
- Global hotkey functionality across different system configurations
- iCloud synchronization scenarios
- Multi-monitor support validation

## 11. Deployment and Distribution

### 11.1. Code Signing and Notarization
- Developer ID signing for distribution outside Mac App Store
- Hardened runtime enabled with appropriate entitlements
- Automatic notarization in CI/CD pipeline

### 11.2. Update Mechanism
- Implement Sparkle framework for secure automatic updates
- Delta updates for efficient bandwidth usage
- Staged rollout capability for major updates

## Conclusion

This revised PRD addresses the critical technical challenges identified in the original specification while maintaining the core vision of a lightweight, efficient macOS utility. The key improvements focus on:

1. **Reliability:** Using modern, supported APIs instead of deprecated ones
2. **Performance:** Optimized data storage and monitoring strategies
3. **Maintainability:** Protocol-based architecture for better testing and extensibility
4. **User Experience:** Better system integration and accessibility support
5. **Security:** Privacy-conscious design with optional encryption

The technical foundation is now solid for building a production-quality application that honors the efficiency and simplicity of classic utilities while leveraging modern macOS capabilities.