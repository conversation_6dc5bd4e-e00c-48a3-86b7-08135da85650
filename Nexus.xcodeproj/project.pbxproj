// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		A1000001000000000000001 /* NexusApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000002000000000000001 /* NexusApp.swift */; };
		A1000003000000000000001 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000004000000000000001 /* ContentView.swift */; };
		A1000005000000000000001 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A1000006000000000000001 /* Assets.xcassets */; };
		A1000007000000000000001 /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A1000008000000000000001 /* Preview Assets.xcassets */; };
		A1000043000000000000001 /* NexusModule.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000038000000000000001 /* NexusModule.swift */; };
		A1000044000000000000001 /* ModuleManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000039000000000000001 /* ModuleManager.swift */; };
		A1000045000000000000001 /* SearchViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000040000000000000001 /* SearchViewModel.swift */; };
		A1000046000000000000001 /* AppConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000041000000000000001 /* AppConfiguration.swift */; };
		A1000047000000000000001 /* ConfigurationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000042000000000000001 /* ConfigurationManager.swift */; };
		A1000051000000000000001 /* EnvironmentKeys.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000050000000000000001 /* EnvironmentKeys.swift */; };
		A1000054000000000000001 /* NexusTextView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000052000000000000001 /* NexusTextView.swift */; };
		A1000055000000000000001 /* EditorView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000053000000000000001 /* EditorView.swift */; };
		A1000059000000000000001 /* DatabaseManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000057000000000000001 /* DatabaseManager.swift */; };
		A1000060000000000000001 /* DataService.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000058000000000000001 /* DataService.swift */; };
		A1000026000000000000001 /* Markdown in Frameworks */ = {isa = PBXBuildFile; productRef = A1000027000000000000001 /* Markdown */; };
		A1000028000000000000001 /* Highlightr in Frameworks */ = {isa = PBXBuildFile; productRef = A1000029000000000000001 /* Highlightr */; };
		A1000030000000000000001 /* KeyboardShortcuts in Frameworks */ = {isa = PBXBuildFile; productRef = A1000031000000000000001 /* KeyboardShortcuts */; };
		A1000032000000000000001 /* SQLite in Frameworks */ = {isa = PBXBuildFile; productRef = A1000033000000000000001 /* SQLite */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		A1000009000000000000001 /* Nexus.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Nexus.app; sourceTree = BUILT_PRODUCTS_DIR; };
		A1000002000000000000001 /* NexusApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NexusApp.swift; sourceTree = "<group>"; };
		A1000004000000000000001 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		A1000006000000000000001 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		A1000008000000000000001 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		A1000010000000000000001 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		A1000038000000000000001 /* NexusModule.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NexusModule.swift; sourceTree = "<group>"; };
		A1000039000000000000001 /* ModuleManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ModuleManager.swift; sourceTree = "<group>"; };
		A1000040000000000000001 /* SearchViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SearchViewModel.swift; sourceTree = "<group>"; };
		A1000041000000000000001 /* AppConfiguration.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppConfiguration.swift; sourceTree = "<group>"; };
		A1000042000000000000001 /* ConfigurationManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ConfigurationManager.swift; sourceTree = "<group>"; };
		A1000050000000000000001 /* EnvironmentKeys.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EnvironmentKeys.swift; sourceTree = "<group>"; };
		A1000052000000000000001 /* NexusTextView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NexusTextView.swift; sourceTree = "<group>"; };
		A1000053000000000000001 /* EditorView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EditorView.swift; sourceTree = "<group>"; };
		A1000057000000000000001 /* DatabaseManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DatabaseManager.swift; sourceTree = "<group>"; };
		A1000058000000000000001 /* DataService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DataService.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		A1000011000000000000001 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1000026000000000000001 /* Markdown in Frameworks */,
				A1000028000000000000001 /* Highlightr in Frameworks */,
				A1000030000000000000001 /* KeyboardShortcuts in Frameworks */,
				A1000032000000000000001 /* SQLite in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		A1000012000000000000001 = {
			isa = PBXGroup;
			children = (
				A1000013000000000000001 /* Nexus */,
				A1000014000000000000001 /* Products */,
			);
			sourceTree = "<group>";
		};
		A1000014000000000000001 /* Products */ = {
			isa = PBXGroup;
			children = (
				A1000009000000000000001 /* Nexus.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		A1000013000000000000001 /* Nexus */ = {
			isa = PBXGroup;
			children = (
				A1000002000000000000001 /* NexusApp.swift */,
				A1000004000000000000001 /* ContentView.swift */,
				A1000048000000000000001 /* Core */,
				A1000049000000000000001 /* Models */,
				A1000056000000000000001 /* Editor */,
				A1000006000000000000001 /* Assets.xcassets */,
				A1000010000000000000001 /* Info.plist */,
				A1000015000000000000001 /* Preview Content */,
			);
			path = Nexus;
			sourceTree = "<group>";
		};
		A1000048000000000000001 /* Core */ = {
			isa = PBXGroup;
			children = (
				A1000038000000000000001 /* NexusModule.swift */,
				A1000039000000000000001 /* ModuleManager.swift */,
				A1000040000000000000001 /* SearchViewModel.swift */,
				A1000042000000000000001 /* ConfigurationManager.swift */,
				A1000050000000000000001 /* EnvironmentKeys.swift */,
			);
			path = Core;
			sourceTree = "<group>";
		};
		A1000049000000000000001 /* Models */ = {
			isa = PBXGroup;
			children = (
				A1000041000000000000001 /* AppConfiguration.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		A1000056000000000000001 /* Editor */ = {
			isa = PBXGroup;
			children = (
				A1000052000000000000001 /* NexusTextView.swift */,
				A1000053000000000000001 /* EditorView.swift */,
			);
			path = Editor;
			sourceTree = "<group>";
		};
		A1000015000000000000001 /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				A1000008000000000000001 /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		A1000016000000000000001 /* Nexus */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A1000017000000000000001 /* Build configuration list for PBXNativeTarget "Nexus" */;
			buildPhases = (
				A1000018000000000000001 /* Sources */,
				A1000011000000000000001 /* Frameworks */,
				A1000019000000000000001 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Nexus;
			packageProductDependencies = (
				A1000027000000000000001 /* Markdown */,
				A1000029000000000000001 /* Highlightr */,
				A1000031000000000000001 /* KeyboardShortcuts */,
				A1000033000000000000001 /* SQLite */,
			);
			productName = Nexus;
			productReference = A1000009000000000000001 /* Nexus.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		A1000020000000000000001 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1500;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					A1000016000000000000001 = {
						CreatedOnToolsVersion = 15.0;
					};
				};
			};
			buildConfigurationList = A1000021000000000000001 /* Build configuration list for PBXProject "Nexus" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = A1000012000000000000001;
			packageReferences = (
				A1000034000000000000001 /* XCRemoteSwiftPackageReference "swift-markdown" */,
				A1000035000000000000001 /* XCRemoteSwiftPackageReference "Highlightr" */,
				A1000036000000000000001 /* XCRemoteSwiftPackageReference "KeyboardShortcuts" */,
				A1000037000000000000001 /* XCRemoteSwiftPackageReference "SQLite.swift" */,
			);
			productRefGroup = A1000014000000000000001 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				A1000016000000000000001 /* Nexus */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		A1000019000000000000001 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1000007000000000000001 /* Preview Assets.xcassets in Resources */,
				A1000005000000000000001 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		A1000018000000000000001 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1000003000000000000001 /* ContentView.swift in Sources */,
				A1000001000000000000001 /* NexusApp.swift in Sources */,
				A1000043000000000000001 /* NexusModule.swift in Sources */,
				A1000044000000000000001 /* ModuleManager.swift in Sources */,
				A1000045000000000000001 /* SearchViewModel.swift in Sources */,
				A1000046000000000000001 /* AppConfiguration.swift in Sources */,
				A1000047000000000000001 /* ConfigurationManager.swift in Sources */,
				A1000051000000000000001 /* EnvironmentKeys.swift in Sources */,
				A1000054000000000000001 /* NexusTextView.swift in Sources */,
				A1000055000000000000001 /* EditorView.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		A1000022000000000000001 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		A1000023000000000000001 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		A1000024000000000000001 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Nexus/Nexus.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"Nexus/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Nexus/Info.plist;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.nexus.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		A1000025000000000000001 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Nexus/Nexus.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"Nexus/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Nexus/Info.plist;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.nexus.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		A1000021000000000000001 /* Build configuration list for PBXProject "Nexus" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A1000022000000000000001 /* Debug */,
				A1000023000000000000001 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A1000017000000000000001 /* Build configuration list for PBXNativeTarget "Nexus" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A1000024000000000000001 /* Debug */,
				A1000025000000000000001 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		A1000034000000000000001 /* XCRemoteSwiftPackageReference "swift-markdown" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/apple/swift-markdown.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 0.3.0;
			};
		};
		A1000035000000000000001 /* XCRemoteSwiftPackageReference "Highlightr" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/raspu/Highlightr.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.1.2;
			};
		};
		A1000036000000000000001 /* XCRemoteSwiftPackageReference "KeyboardShortcuts" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/sindresorhus/KeyboardShortcuts.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.16.0;
			};
		};
		A1000037000000000000001 /* XCRemoteSwiftPackageReference "SQLite.swift" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/stephencelis/SQLite.swift.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 0.14.1;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		A1000027000000000000001 /* Markdown */ = {
			isa = XCSwiftPackageProductDependency;
			package = A1000034000000000000001 /* XCRemoteSwiftPackageReference "swift-markdown" */;
			productName = Markdown;
		};
		A1000029000000000000001 /* Highlightr */ = {
			isa = XCSwiftPackageProductDependency;
			package = A1000035000000000000001 /* XCRemoteSwiftPackageReference "Highlightr" */;
			productName = Highlightr;
		};
		A1000031000000000000001 /* KeyboardShortcuts */ = {
			isa = XCSwiftPackageProductDependency;
			package = A1000036000000000000001 /* XCRemoteSwiftPackageReference "KeyboardShortcuts" */;
			productName = KeyboardShortcuts;
		};
		A1000033000000000000001 /* SQLite */ = {
			isa = XCSwiftPackageProductDependency;
			package = A1000037000000000000001 /* XCRemoteSwiftPackageReference "SQLite.swift" */;
			productName = SQLite;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = A1000020000000000000001 /* Project object */;
}
